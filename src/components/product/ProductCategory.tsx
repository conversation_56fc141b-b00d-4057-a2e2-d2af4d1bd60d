'use client'
import {
    Box,
    Container,
    Flex,
    Heading,
    HTMLChakraProps,
    Text
} from '@chakra-ui/react'
import { useRef, useState, useEffect } from 'react'
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa'
import ProductCard from '../product/ProductCard'
import { ProductItem } from '@/types/product'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

interface ProductCategoryProps {
    items: ProductItem[]
    title?: string
    maxWidth?: HTMLChakraProps<'div'>['maxW']
}

const ProductCategory: React.FC<ProductCategoryProps> = ({
    items,
    title,
    maxWidth = { lg: '100%' }
}) => {
    const t = useTranslations();
    const [isDragging, setIsDragging] = useState(false)
    const [startX, setStartX] = useState(0)
    const [scrollLeft, setScrollLeft] = useState(0)
    const containerRef = useRef<HTMLDivElement>(null)

    const [showLeftButton, setShowLeftButton] = useState(false)
    const [showRightButton, setShowRightButton] = useState(true)

    const handleMouseDown = (e: React.MouseEvent) => {
        if (!containerRef.current) return
        setIsDragging(true)
        setStartX(e.pageX - containerRef.current.offsetLeft)
        setScrollLeft(containerRef.current.scrollLeft)
    }

    const handleMouseMove = (e: React.MouseEvent) => {
        if (!isDragging || !containerRef.current) return
        e.preventDefault()
        const x = e.pageX - containerRef.current.offsetLeft
        const walk = (x - startX) * 2
        containerRef.current.scrollLeft = scrollLeft - walk
    }

    const handleMouseUp = () => setIsDragging(false)
    const handleMouseLeave = () => setIsDragging(false)

    const handleTouchStart = (e: React.TouchEvent) => {
        if (!containerRef.current) return
        setIsDragging(true)
        setStartX(e.touches[0].pageX - containerRef.current.offsetLeft)
        setScrollLeft(containerRef.current.scrollLeft)
    }

    const handleTouchMove = (e: React.TouchEvent) => {
        if (!isDragging || !containerRef.current) return
        const x = e.touches[0].pageX - containerRef.current.offsetLeft
        const walk = (x - startX) * 2
        containerRef.current.scrollLeft = scrollLeft - walk
    }

    const handleTouchEnd = () => setIsDragging(false)

    const scroll = (direction: 'left' | 'right') => {
        if (!containerRef.current) return
        const scrollAmount = direction === 'left' ? -300 : 300
        containerRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' })
        setTimeout(updateButtonVisibility, 300)
    }

    const updateButtonVisibility = () => {
        if (!containerRef.current) return
        const { scrollLeft, scrollWidth, clientWidth } = containerRef.current
        setShowLeftButton(scrollLeft > 0)
        setShowRightButton(scrollLeft < scrollWidth - clientWidth)
    }

    useEffect(() => {
        const container = containerRef.current
        if (!container) return

        const handleScroll = () => {
            requestAnimationFrame(updateButtonVisibility)
        }

        container.addEventListener('scroll', handleScroll)
        updateButtonVisibility()

        return () => {
            container.removeEventListener('scroll', handleScroll)
        }
    }, [])

    return (
        <Box py={{ base: 6, md: 8 }}>
            <Container maxW={maxWidth}>
                <Flex
                    justifyContent="space-between"
                    alignItems={{ base: 'start', md: 'center' }}
                    gap={4}
                    mb={6}
                    flexDirection={{ base: 'column', md: 'row' }}
                >
                    {title && (
                        <Heading
                            size={{ base: 'lg', md: 'xl', lg: '2xl' }}
                            color="gray.800"
                            fontWeight="bold"
                        >
                            {title}
                        </Heading>
                    )}
                    <Flex
                        flexDirection={{ base: 'column', sm: 'row' }}
                        alignItems={{ base: 'start', sm: 'center' }}
                        gap={{ base: 2, sm: 4 }}
                    >
                        <Text
                            fontSize={{ base: "sm", md: "md" }}
                            color="gray.600"
                            whiteSpace="nowrap"
                        >
                            {t('showing')}{" "}
                            <Box as="span" fontWeight="bold" color="gray.800">
                                1 - {Math.min(items.length, 12)}
                            </Box>{" "}
                            {t('of')}{" "}
                            <Box as="span" fontWeight="bold" color="gray.800">
                                {items.length}
                            </Box>
                        </Text>
                        <Link href="/marketplace">
                            <Text
                                fontSize={{ base: "sm", md: "md" }}
                                color="blue.600"
                                textDecoration="underline"
                                fontWeight="semibold"
                                _hover={{
                                    color: 'blue.800',
                                    textDecoration: 'none'
                                }}
                                whiteSpace="nowrap"
                            >
                                {t('viewAll')}
                            </Text>
                        </Link>
                    </Flex>
                </Flex>

                <Box pb={8} position="relative" h="full">
                    {showLeftButton && (
                        <ScrollButton position="left" onClick={() => scroll('left')} />
                    )}
                    <Box
                        ref={containerRef}
                        display="flex"
                        alignItems={'start'}
                        overflowX="auto"
                        overflowY="hidden"
                        gap={{ base: 3, md: 6 }}
                        css={{
                            '&::-webkit-scrollbar': { display: 'none' },
                            msOverflowStyle: 'none',
                            scrollbarWidth: 'none',
                        }}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        onMouseLeave={handleMouseLeave}
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                        userSelect={"none"}
                        cursor={isDragging ? 'grabbing' : 'grab'}>
                        {items.map((item) => (
                            <ProductCard
                                imageContainerProps={{
                                    h: { base: '200px', md: '300px' },
                                    w: { base: '170px', md: '250px' },
                                }}
                                imageProps={{
                                    maxW: { base: '140px', md: '220px' },
                                    maxH: { base: '160px', md: '240px' },
                                }}
                                key={item.id}
                                item={item} />
                        ))}
                    </Box>
                    {showRightButton && (
                        <ScrollButton position="right" onClick={() => scroll('right')} />
                    )}
                </Box>
            </Container>
        </Box>
    )
}

export default ProductCategory

const ScrollButton = ({
    position,
    onClick,
}: {
    position: 'left' | 'right'
    onClick: () => void
}) => (
    <Box
        position="absolute"
        left={position === 'left' ? { base: '0', lg: '-20px' } : undefined}
        right={position === 'right' ? { base: '0', lg: '-20px' } : undefined}
        top="38%"
        transform="translateY(-50%)"
        zIndex="1"
    >
        <Box
            cursor="pointer"
            bg="white"
            boxShadow="lg"
            borderRadius="full"
            borderWidth="1px"
            borderColor="gray.200"
            h="36px"
            w="36px"
            display="flex"
            justifyContent="center"
            alignItems="center"
            onClick={onClick}
            color="gray.600"
        >
            {position === 'left' ? <FaChevronLeft size="16" /> : <FaChevronRight size="16" />}
        </Box>
    </Box>
)
